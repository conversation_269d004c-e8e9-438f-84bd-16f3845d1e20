import { getTranslations } from "next-intl/server";
import LogoAtelier from "@/assets/imgs/logo-atelier.svg";
import LogoReadyToWear from "@/assets/imgs/logo-ready-to-wear.svg";
import LogoSouDessas from "@/assets/imgs/logo-sou-dessas.svg";
import { cn } from "@/lib/utils";
import Link from "next/link";

type Props = {
  className?: string;
};

const Footer = async ({ className }: Props) => {
  const t = await getTranslations("footer");

  const logos = [
    {
      name: "ready-to-wear",
      component: LogoReadyToWear,
      url: "https://www.instagram.com/rose.palhares.rtw/",
    },
    {
      name: "atelier",
      component: LogoAtelier,
      url: "https://www.instagram.com/rosepalhares.atelier/",
    },
    {
      name: "sou-dessas",
      component: LogoSouDessas,
      url: "https://www.instagram.com/soudessasangola/",
      className: "max-w-40",
    },
  ];

  return (
    <footer className="text-muted-foreground container-x max-sm:px-0">
      <section
        className={cn(
          "bg-primary container-x flex flex-col gap-8 pt-12 pb-6 max-sm:items-center sm:gap-16",
          className,
        )}
      >
        <nav className="flex justify-between gap-8">
          {logos.map((logo) => (
            <Link
              key={logo.name}
              href={logo.url}
              target="_blank"
              rel="noopener noreferrer"
              className="block"
            >
              <logo.component
                className={cn(
                  "hocus:scale-105 h-auto w-full max-w-50 duration-300 ease-in-out",
                  logo.className,
                )}
              />
            </Link>
          ))}
        </nav>
        <p className="max-sm:text-center">{t("copyright")}</p>
      </section>
    </footer>
  );
};

export default Footer;
