{"name": "wa-2025", "version": "2025.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@next/third-parties": "^15.3.4", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@svgr/webpack": "^8.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "lucide-react": "^0.523.0", "next": "15.3.5", "next-intl": "^4.3.1", "next-themes": "^0.4.6", "nodemailer": "^7.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "prettier": "^3.6.1", "prettier-plugin-tailwindcss": "^0.6.13", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}