import { getTranslations } from "next-intl/server";
import React from "react";
import HeroImg1 from "@/assets/imgs/hero-1.jpg";
import HeroImg2 from "@/assets/imgs/hero-2.jpg";
import SuperBrand from "@/assets/imgs/super-brands.svg";
import * as z from "zod/v4-mini";
import {
  Carousel,
  CarouselContent,
  CarouselDots,
  CarouselItem,
} from "@/components/ui/carousel";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

const slideImages = {
  "hero-1": HeroImg1,
  "hero-2": HeroImg2,
} as const;

const slideSchema = z.object({
  title: z.array(z.string()),
  image: z.enum(Object.keys(slideImages) as [keyof typeof slideImages]),
});

type Props = {
  className?: string;
};

const HeroSlides = async ({ className }: Props) => {
  const t = await getTranslations("hero");
  const tCommon = await getTranslations("common");

  const slides = z
    .array(slideSchema)
    .parse(t.raw("slides"))
    .map((slide) => ({
      title: slide.title,
      image: slideImages[slide.image],
    }));

  return (
    <Carousel
      className={cn(
        "bg-background relative grid h-screen max-h-[50rem] min-h-[30rem]",
        className,
      )}
    >
      <CarouselContent className="ml-0 h-full">
        {slides.map((s, index) => (
          <CarouselItem key={index} className="h-full w-full pl-0">
            <article className="grid-stack relative grid h-full w-full items-end">
              <Image
                src={s.image}
                alt={s.title.join(" ")}
                className="absolute top-0 left-0 h-full w-full object-cover object-top"
                quality={90}
                placeholder="blur"
              />
              <div className="stack-item container-full relative text-white uppercase">
                <h3 className="text-xl text-shadow-black/20 text-shadow-lg sm:text-3xl">
                  {s.title[0]}
                </h3>
                <h1 className="text-5xl text-shadow-black/20 text-shadow-lg sm:text-6xl lg:text-7xl">
                  {s.title[1]}
                </h1>
                <SuperBrand className="mt-24 ml-auto h-auto w-24 sm:w-36" />
              </div>
            </article>
          </CarouselItem>
        ))}
      </CarouselContent>

      <div className="container-x absolute inset-x-0 bottom-0 !pb-0">
        <Button
          size="xl"
          className="bg-primary/50 w-fit translate-y-5 leading-none uppercase"
        >
          {tCommon("visitUs")}
        </Button>
      </div>
      <CarouselDots className="absolute bottom-10 left-1/2 z-10 -translate-x-1/2" />
    </Carousel>
  );
};

export default HeroSlides;
