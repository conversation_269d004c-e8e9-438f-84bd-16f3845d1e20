import { LocaleType, routing } from "@/i18n/routing";
import { setRequestLocale } from "next-intl/server";

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}

const HomePage = async ({
  params,
}: {
  params: Promise<{ locale: LocaleType }>;
}) => {
  const { locale } = await params;

  // Enable static rendering
  setRequestLocale(locale);

  return (
    <main className="">
      <div className="flex min-h-screen items-center justify-center">
        <h1 className="text-center text-4xl font-bold">Home Page</h1>
      </div>
    </main>
  );
};

export default HomePage;
