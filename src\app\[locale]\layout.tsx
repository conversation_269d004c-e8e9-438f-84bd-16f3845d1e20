import "../globals.css";

import { NextIntlClientProvider, hasLocale } from "next-intl";
import { notFound } from "next/navigation";
import { LOCALES, routing } from "@/i18n/routing";
import { getTranslations, setRequestLocale } from "next-intl/server";
import { agendaOne } from "@/app/fonts";
import { Toaster } from "sonner";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: (typeof LOCALES)[number] }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "Metadata" });

  return {
    title: t("title"),
  };
}

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}>) {
  const { locale } = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  // Enable static rendering
  setRequestLocale(locale);

  return (
    <html lang={locale}>
      <NextIntlClientProvider>
        <body className={`${agendaOne.variable} antialiased`}>
          {children}
          <Toaster />
        </body>
      </NextIntlClientProvider>
    </html>
  );
}
